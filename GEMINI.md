This file contains instructions for <PERSON>. By default, I will follow these instructions for all files in this project. You can create other GEMINI.md files in subdirectories to provide more specific instructions for those parts of the project.

# Netlify
- Netlify build logs correctly show ISR pages with '(ISR: 60 Seconds)' notation when revalidate is properly configured in getStaticProps.
- Netlify webhook endpoints are working correctly - health check shows all environment variables configured and test-revalidate successfully revalidates pages with ISR.
- Sanity Studio data updates work for players but not for partners in the deployed app, indicating potential ISR or webhook configuration issues for specific content types.
- ISR updates require hard refresh (Ctrl+F5) to see changes; regular refresh doesn't show updated content due to browser/CDN caching.

# Teams
- Teams should be sorted by calculated ELO in descending order, and team ELO calculation should only use players with valid ELO > 0, showing 'No ELO data available' only when ALL players have 0 or missing ELO.
- User prefers teams page to have toggle for ELO calculation modes: enabled uses all valid players average, disabled uses top-3 players average with separate unranked teams tab for teams with <3 valid players.

# Players
- User prefers to filter out players with ELO 0 from appearing in player lists or displays.
- User prefers to filter out players with ELO 0 from player comparison and analysis scripts.
- User prefers player import scripts to only update ELO and nickname fields, preserve all other existing data, conditionally remove players without photos who aren't in new data, and keep players with photos for manual review even if not in new import data.
- User prefers role badges (admin/developer/moderator) to be positioned on the right side of player cards rather than next to the name, and without white/border outlines for cleaner appearance.
- User prefers role badges to be larger and rectangular/rounded rather than circular for better visibility and design.
- User prefers role system with only 'creator' (orange background) and 'support' (green background) roles selectable, with default being no role.
- User prefers to delete and rewrite admin gradient functionality completely rather than fixing existing implementation, and wants to use task management for organizing the work.
- User prefers admin names to have glowing gradient styling and admin/developer/moderator roles displayed to the right of names, with simple implementation since changes won't be frequent.

# Data Analysis
- User prefers to filter out small rounding errors (like <1 ELO difference) from data comparison analysis scripts to focus on meaningful differences only.

# User Interface
- Discord tags are now formatted as @username (without #numbers) instead of the previous username#1234 format.
- User prefers popup modals for displaying detailed information when clicking on entities (teams, pilots) rather than navigation to separate pages.
- Added click-outside-to-close functionality to all modal popups (players, teams, tournaments, news, support requests) and language selector dropdown using onClick handlers on backdrop divs and stopPropagation on modal content.
- User replaced "QRP HLTV" text in header with QRP_LOGO.webp image using Next.js Image component with proper sizing and accessibility.
- User prefers single-card carousels with side navigation arrows, slide indicators, full-width cards with background images, and click-to-navigate-and-open-modal interaction pattern for content cards.
- For carousel video posts: use videos as background elements with autoplay muted, loop continuously, maintain same layout as image posts, include playsInline for mobile compatibility.
- User prefers separate optional photo and video and video inputs for tournaments instead of a single combined photo/video input field.
- User prefers admin tribute pages to include a humorous disclaimer explaining it's not a real admin panel to manage user expectations who might expect actual admin access.
- User prefers footer components to have more spacing/padding from the bottom of the screen rather than being positioned at the very edge.
- User prefers partners section to display 3 partners per line instead of 2 for better layout utilization.
- User prefers wider grid layouts for partners section when displaying 3 columns, finding the current layout too narrow/tucked.

# Development Preferences
- User prefers simple, personal commit messages for small team work rather than formal conventional commit format with detailed explanations.
- User prefers all text fields in schemas to use rich text format (type: 'array', of: [{ type: 'block' }]) instead of simple text fields for better formatting support.
- Partners schema should use rich text format for partner description field to maintain consistency with other text fields in the codebase.
- User prefers automatic backup solutions over manual backup scripts for CMS data protection.
- Development workflow: run 'sanity dev' in root directory (starts on localhost:3333), then cd to frontend directory and run 'npm run dev' (starts on localhost:3000).
- User prefers not to generate detailed guides or documentation unless specifically necessary or requested.
- User prefers using CMS for admin functionality over custom admin pages like support-requests.tsx.
- User prefers modifying language context/translations directly rather than using string manipulation methods like .replace() to handle text formatting issues.
- `npm dev` shows `fetchPriority` prop warning on Next.js Image component and title element receiving array children warning that should be addressed for proper React/Next.js compliance.
- User prefers to commit changes to separate branches so they can merge into main at their discretion rather than committing directly to main.

# Project Details
- User purchased domain qrp-hltv.com for the CarX HLTV project.
- User has successfully deployed Sanity Studio to hosted version at https://carx-hltv.sanity.studio/ in addition to the embedded studio at /studio path.

# User Tracking
- User prefers to implement real-time user tracking without GDPR/privacy compliance concerns, tracking unanonymized data where possible, and integrating with existing role-based admin system for analytics dashboards.